07-21 11:51:15.927 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 9440 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-21 11:51:15.928 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-21 11:51:15.930 [main] INFO  com.zy.dam.DamApplication - No active profile set, falling back to 1 default profile: "default"
07-21 11:51:16.584 [main] WARN  o.s.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'securityHeadersFilter' defined in class path resource [com/zy/config/SecurityConfig.class]: Cannot register bean definition [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=securityConfig; factoryMethodName=securityHeadersFilter; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/zy/config/SecurityConfig.class]] for bean 'securityHeadersFilter': There is already [Generic bean: class [com.zy.config.SecurityHeadersFilter]; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [F:\work\ticai\ticai-dam\target\classes\com\zy\config\SecurityHeadersFilter.class]] bound.
07-21 11:51:16.591 [main] INFO  o.s.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
07-21 11:51:16.602 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The bean 'securityHeadersFilter', defined in class path resource [com/zy/config/SecurityConfig.class], could not be registered. A bean with that name has already been defined in file [F:\work\ticai\ticai-dam\target\classes\com\zy\config\SecurityHeadersFilter.class] and overriding is disabled.

Action:

Consider renaming one of the beans or enabling overriding by setting spring.main.allow-bean-definition-overriding=true

07-21 11:51:32.523 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 12552 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-21 11:51:32.523 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-21 11:51:32.525 [main] INFO  com.zy.dam.DamApplication - No active profile set, falling back to 1 default profile: "default"
07-21 11:51:32.945 [main] WARN  o.s.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'securityHeadersFilter' defined in class path resource [com/zy/config/SecurityConfig.class]: Cannot register bean definition [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=securityConfig; factoryMethodName=securityHeadersFilter; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/zy/config/SecurityConfig.class]] for bean 'securityHeadersFilter': There is already [Generic bean: class [com.zy.config.SecurityHeadersFilter]; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [F:\work\ticai\ticai-dam\target\classes\com\zy\config\SecurityHeadersFilter.class]] bound.
07-21 11:51:32.952 [main] INFO  o.s.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
07-21 11:51:32.960 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The bean 'securityHeadersFilter', defined in class path resource [com/zy/config/SecurityConfig.class], could not be registered. A bean with that name has already been defined in file [F:\work\ticai\ticai-dam\target\classes\com\zy\config\SecurityHeadersFilter.class] and overriding is disabled.

Action:

Consider renaming one of the beans or enabling overriding by setting spring.main.allow-bean-definition-overriding=true

07-21 11:54:25.143 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 33900 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-21 11:54:25.143 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-21 11:54:25.145 [main] INFO  com.zy.dam.DamApplication - No active profile set, falling back to 1 default profile: "default"
07-21 11:54:25.578 [main] WARN  o.s.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'securityHeadersFilter' defined in class path resource [com/zy/config/SecurityConfig.class]: Cannot register bean definition [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=securityConfig; factoryMethodName=securityHeadersFilter; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/zy/config/SecurityConfig.class]] for bean 'securityHeadersFilter': There is already [Generic bean: class [com.zy.config.SecurityHeadersFilter]; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [F:\work\ticai\ticai-dam\target\classes\com\zy\config\SecurityHeadersFilter.class]] bound.
07-21 11:54:25.584 [main] INFO  o.s.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
07-21 11:54:25.592 [main] ERROR org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The bean 'securityHeadersFilter', defined in class path resource [com/zy/config/SecurityConfig.class], could not be registered. A bean with that name has already been defined in file [F:\work\ticai\ticai-dam\target\classes\com\zy\config\SecurityHeadersFilter.class] and overriding is disabled.

Action:

Consider renaming one of the beans or enabling overriding by setting spring.main.allow-bean-definition-overriding=true

07-21 11:56:15.900 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-21 11:56:15.903 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 12132 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-21 11:56:15.904 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-21 11:56:16.499 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-21 11:56:16.501 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-21 11:56:16.523 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
07-21 11:56:16.694 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-21 11:56:16.966 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-21 11:56:16.974 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-21 11:56:16.975 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-21 11:56:16.975 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-21 11:56:17.135 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-21 11:56:17.135 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1205 ms
07-21 11:56:17.165 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-21 11:56:17.233 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-21 11:56:17.687 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-21 11:56:18.607 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-21 11:56:18.607 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-21 11:56:18.615 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-21 11:56:18.615 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-21 11:56:18.615 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-21 11:56:18.615 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-21T11:56:18.615810900+08:00[Asia/Shanghai]
07-21 11:56:18.665 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-21 11:56:18.682 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-21 11:56:18.698 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-21 11:56:18.715 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-21T03:56:18
07-21 11:56:18.715 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-21T03:56:18
07-21 11:56:18.735 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1753070178716
07-21 11:56:18.735 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1753070178000
07-21 11:56:18.735 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 716 毫秒
07-21 11:56:18.735 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-21 11:56:18.735 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-21 11:56:19.411 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-21 11:56:19.417 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-21 11:56:19.418 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-21 11:56:19.418 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-21 11:56:19.418 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-21 11:56:19.419 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-21 11:56:19.419 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-21 11:56:19.419 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@38213efa
07-21 11:56:20.648 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
07-21 11:56:20.654 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-21 11:56:20.699 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-21 11:56:20.739 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-21 11:56:20.761 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-21 11:56:21.965 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
07-21 11:56:21.965 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-21 11:56:21.965 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-21 11:56:21.975 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 7.555 seconds (JVM running for 8.287)
07-21 11:56:36.997 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-21 11:56:36.998 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-21 11:56:36.999 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
07-21 11:56:57.250 [http-nio-20000-exec-3] WARN  org.apache.catalina.util.SessionIdGeneratorBase - Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [143] milliseconds.
07-21 11:56:57.339 [http-nio-20000-exec-3] ERROR org.springframework.web.servlet.HandlerExecutionChain - HandlerInterceptor.afterCompletion threw exception
java.lang.NullPointerException: null
	at com.zy.config.SessionConfig$CookieSecurityResponseWrapper.setHeader(SessionConfig.java:110)
	at com.zy.config.CookieSecurityInterceptor.afterCompletion(CookieSecurityInterceptor.java:32)
	at org.springframework.web.servlet.HandlerExecutionChain.triggerAfterCompletion(HandlerExecutionChain.java:178)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1163)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
07-21 11:57:00.093 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 11:57:00.111 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 11:57:00.127 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 11:57:00.143 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-21 11:57:17.358 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-21 11:57:17.532 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-21 11:57:17.532 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-21 11:57:17.532 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-21 11:57:17.533 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-21 11:57:17.657 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-21 11:57:17.838 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
