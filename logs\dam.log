07-17 09:27:01.924 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-17 09:27:01.931 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 1808 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-17 09:27:01.932 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-17 09:27:02.752 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-17 09:27:02.754 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-17 09:27:02.784 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
07-17 09:27:03.009 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-17 09:27:03.357 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-17 09:27:03.366 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-17 09:27:03.366 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-17 09:27:03.366 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-17 09:27:03.536 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-17 09:27:03.536 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1567 ms
07-17 09:27:03.566 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-17 09:27:03.630 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-17 09:27:04.169 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-17 09:27:05.099 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-17 09:27:05.099 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-17 09:27:05.106 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-17 09:27:05.106 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-17 09:27:05.106 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-17 09:27:05.106 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-17T09:27:05.106770400+08:00[Asia/Shanghai]
07-17 09:27:05.165 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-17 09:27:05.188 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-17 09:27:05.210 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-17 09:27:05.232 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-17T01:27:06
07-17 09:27:05.232 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-17T01:27:06
07-17 09:27:05.253 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1752715625232
07-17 09:27:05.253 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1752715626000
07-17 09:27:05.254 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 768 毫秒
07-17 09:27:05.254 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-17 09:27:05.254 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-17 09:27:06.050 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-17 09:27:06.057 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-17 09:27:06.057 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-17 09:27:06.057 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-17 09:27:06.058 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-17 09:27:06.058 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-17 09:27:06.058 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-17 09:27:06.058 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2d2fe68a
07-17 09:27:06.241 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-17 09:27:06.306 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-17 09:27:06.371 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-17 09:27:06.399 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-17 09:27:06.576 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-17 09:27:06.576 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-17 09:27:06.587 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 6.206 seconds (JVM running for 7.458)
07-17 09:28:00.099 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:28:00.123 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:28:00.146 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:28:00.169 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:29:00.070 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:29:00.093 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:29:00.115 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:29:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:29:36.347 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-17 09:29:36.347 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-17 09:29:36.348 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
07-17 09:29:43.925 [http-nio-20000-exec-6] WARN  org.apache.catalina.util.SessionIdGeneratorBase - Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [126] milliseconds.
07-17 09:29:44.053 [http-nio-20000-exec-6] ERROR org.springframework.web.servlet.HandlerExecutionChain - HandlerInterceptor.afterCompletion threw exception
java.lang.NullPointerException: null
	at com.zy.config.SessionConfig$CookieSecurityResponseWrapper.setHeader(SessionConfig.java:110)
	at com.zy.config.CookieSecurityInterceptor.afterCompletion(CookieSecurityInterceptor.java:32)
	at org.springframework.web.servlet.HandlerExecutionChain.triggerAfterCompletion(HandlerExecutionChain.java:178)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1163)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SecurityHeadersFilter.doFilter(SecurityHeadersFilter.java:88)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.zy.config.SessionConfig$CookieSecurityFilter.doFilter(SessionConfig.java:79)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:889)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:834)
07-17 09:30:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:30:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:30:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:30:00.164 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:31:00.073 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:31:00.095 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:31:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:31:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:32:00.070 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:32:00.092 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:32:00.114 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:32:00.136 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:32:34.458 [http-nio-20000-exec-7] INFO  com.zy.dam.asset.svc.AmAssetTraceRecordSVC - 记录资产领用操作成功，资产ID：be1eaa6c-62ad-11f0-8c5c-0242ac1c0008
07-17 09:32:34.458 [http-nio-20000-exec-7] INFO  com.zy.dam.asset.svc.AmAssetConsumingSVC - 记录设备领用操作，资产ID：be1eaa6c-62ad-11f0-8c5c-0242ac1c0008，终端号：null，网点ID：null
07-17 09:32:59.581 [http-nio-20000-exec-3] INFO  com.zy.dam.asset.svc.AmAssetTraceRecordSVC - 记录资产退机操作成功，资产ID：be1eaa6c-62ad-11f0-8c5c-0242ac1c0008
07-17 09:32:59.582 [http-nio-20000-exec-3] INFO  com.zy.dam.asset.svc.AmAssetBackSVC - 记录设备退机操作，资产ID：be1eaa6c-62ad-11f0-8c5c-0242ac1c0008，终端号：null，网点ID：null
07-17 09:33:00.073 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:33:00.095 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:33:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:33:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:33:28.047 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-17 09:33:28.210 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-17 09:33:28.210 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-17 09:33:28.210 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-17 09:33:28.211 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-17 09:33:28.442 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-17 09:33:28.713 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-17 09:33:34.294 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-17 09:33:34.294 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 28132 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-17 09:33:34.296 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-17 09:33:35.049 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-17 09:33:35.050 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-17 09:33:35.069 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
07-17 09:33:35.228 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-17 09:33:35.484 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-17 09:33:35.491 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-17 09:33:35.491 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-17 09:33:35.492 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-17 09:33:35.595 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-17 09:33:35.595 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1271 ms
07-17 09:33:35.623 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-17 09:33:35.682 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-17 09:33:36.160 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-17 09:33:37.033 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-17 09:33:37.033 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-17 09:33:37.039 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-17 09:33:37.039 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-17 09:33:37.039 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-17 09:33:37.039 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-17T09:33:37.039818+08:00[Asia/Shanghai]
07-17 09:33:37.092 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-17 09:33:37.113 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-17 09:33:37.133 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-17 09:33:37.154 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-17T01:33:38
07-17 09:33:37.154 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-17T01:33:38
07-17 09:33:37.175 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1752716017154
07-17 09:33:37.175 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1752716018000
07-17 09:33:37.175 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 846 毫秒
07-17 09:33:37.175 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-17 09:33:37.175 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-17 09:33:37.850 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-17 09:33:37.855 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-17 09:33:37.856 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-17 09:33:37.856 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-17 09:33:37.856 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-17 09:33:37.856 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-17 09:33:37.856 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-17 09:33:37.856 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@51d34f02
07-17 09:33:38.047 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-17 09:33:38.092 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-17 09:33:38.147 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-17 09:33:38.165 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-17 09:33:38.329 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-17 09:33:38.330 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-17 09:33:38.340 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 5.624 seconds (JVM running for 6.478)
07-17 09:33:45.247 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-17 09:33:45.247 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-17 09:33:45.250 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
07-17 09:34:00.073 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:34:00.095 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:34:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:34:00.143 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:35:00.067 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:35:00.088 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:35:00.109 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:35:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:36:00.076 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:36:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:36:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:36:00.139 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:37:00.074 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:37:00.094 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:37:00.115 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:37:00.136 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:38:00.069 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:38:00.090 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:38:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:38:00.131 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:39:00.065 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:39:00.093 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:39:00.113 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:39:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:40:00.073 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:40:00.094 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:40:00.115 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:40:00.136 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:41:00.067 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:41:00.088 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:41:00.109 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:41:00.129 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:42:00.072 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:42:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:42:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:42:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:43:00.078 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:43:00.100 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:43:00.122 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:43:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:44:00.073 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:44:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:44:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:44:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:45:00.077 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:45:00.100 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:45:00.122 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:45:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:46:00.067 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:46:00.090 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:46:00.112 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:46:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:47:00.085 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:47:00.108 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:47:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:47:00.152 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:48:00.069 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:48:00.091 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:48:00.113 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:48:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:49:00.081 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:49:00.104 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:49:00.126 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:49:00.149 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:50:00.082 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:50:00.107 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:50:00.132 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:50:00.156 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:51:00.083 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:51:00.107 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:51:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:51:00.152 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:52:00.080 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:52:00.102 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:52:00.125 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:52:00.147 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:53:00.079 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:53:00.101 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:53:00.123 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:53:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:54:00.073 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:54:00.095 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:54:00.117 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:54:00.139 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:54:50.367 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-17 09:54:50.521 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-17 09:54:50.521 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-17 09:54:50.521 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-17 09:54:50.522 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-17 09:54:50.648 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-17 09:54:50.958 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-17 09:54:54.804 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-17 09:54:54.806 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 30160 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-17 09:54:54.808 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-17 09:54:55.704 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-17 09:54:55.705 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-17 09:54:55.727 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
07-17 09:54:55.932 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
07-17 09:54:56.233 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-17 09:54:56.241 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-17 09:54:56.242 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-17 09:54:56.242 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-17 09:54:56.346 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-17 09:54:56.346 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1503 ms
07-17 09:54:56.373 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-17 09:54:56.420 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-17 09:54:56.891 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-17 09:54:57.749 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
07-17 09:54:57.749 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
07-17 09:54:57.755 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
07-17 09:54:57.755 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
07-17 09:54:57.755 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
07-17 09:54:57.755 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-07-17T09:54:57.755055800+08:00[Asia/Shanghai]
07-17 09:54:57.811 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
07-17 09:54:57.834 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
07-17 09:54:57.856 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
07-17 09:54:57.884 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-07-17T01:54:58
07-17 09:54:57.884 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-07-17T01:54:58
07-17 09:54:57.908 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1752717297884
07-17 09:54:57.908 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1752717298000
07-17 09:54:57.908 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 116 毫秒
07-17 09:54:57.908 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
07-17 09:54:57.908 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
07-17 09:54:58.613 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-17 09:54:58.619 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-17 09:54:58.619 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-17 09:54:58.620 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-17 09:54:58.620 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-17 09:54:58.620 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-17 09:54:58.620 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-17 09:54:58.620 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7f7f544a
07-17 09:54:58.813 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-17 09:54:58.860 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-17 09:54:58.912 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-17 09:54:58.931 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-17 09:54:59.106 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-17 09:54:59.106 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-17 09:54:59.118 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 5.944 seconds (JVM running for 6.765)
07-17 09:55:00.107 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:55:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:55:00.153 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:55:00.176 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:55:12.481 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-17 09:55:12.482 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-17 09:55:12.484 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
07-17 09:56:00.076 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:56:00.098 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:56:00.120 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:56:00.142 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:56:07.825 [http-nio-20000-exec-6] INFO  com.zy.dam.asset.svc.AmAssetTraceRecordSVC - 记录资产退机操作成功，资产ID：be1eaa6c-62ad-11f0-8c5c-0242ac1c0008
07-17 09:56:07.825 [http-nio-20000-exec-6] INFO  com.zy.dam.asset.svc.AmAssetBackSVC - 记录设备退机操作，资产ID：be1eaa6c-62ad-11f0-8c5c-0242ac1c0008，终端号：null，网点ID：null
07-17 09:56:47.727 [http-nio-20000-exec-9] INFO  com.zy.dam.asset.svc.AmAssetTraceRecordSVC - 记录资产领用操作成功，资产ID：be1eaa6c-62ad-11f0-8c5c-0242ac1c0008
07-17 09:56:47.727 [http-nio-20000-exec-9] INFO  com.zy.dam.asset.svc.AmAssetConsumingSVC - 记录设备领用操作，资产ID：be1eaa6c-62ad-11f0-8c5c-0242ac1c0008，终端号：null，网点ID：null
07-17 09:57:00.076 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:57:00.098 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:57:00.120 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:57:00.149 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:57:47.069 [http-nio-20000-exec-10] INFO  com.zy.dam.asset.svc.AmAssetTraceRecordSVC - 记录资产退机操作成功，资产ID：be1eaa6c-62ad-11f0-8c5c-0242ac1c0008
07-17 09:57:47.069 [http-nio-20000-exec-10] INFO  com.zy.dam.asset.svc.AmAssetBackSVC - 记录设备退机操作，资产ID：be1eaa6c-62ad-11f0-8c5c-0242ac1c0008，终端号：null，网点ID：null
07-17 09:58:00.069 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:58:00.091 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:58:00.114 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:58:00.136 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:59:00.081 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:59:00.103 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:59:00.125 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 09:59:00.147 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:00:00.080 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:00:00.102 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:00:00.124 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:00:00.146 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:01:00.072 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:01:00.094 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:01:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:01:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:02:00.068 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:02:00.090 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:02:00.112 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:02:00.142 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:03:00.078 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:03:00.100 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:03:00.122 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:03:00.144 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:04:00.090 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:04:00.115 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:04:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:04:00.211 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:05:00.083 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:05:00.107 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:05:00.129 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:05:00.152 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:06:00.072 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:06:00.093 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:06:00.115 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:06:00.136 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:07:00.082 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:07:00.106 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:07:00.128 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:07:00.150 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:08:00.070 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:08:00.094 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:08:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:08:00.137 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:09:00.072 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:09:00.094 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:09:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:09:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:10:00.071 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:10:00.093 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:10:00.115 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:10:00.137 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:11:00.068 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:11:00.090 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:11:00.112 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:11:00.133 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:12:00.080 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:12:00.103 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:12:00.127 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:12:00.149 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:13:00.079 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:13:00.101 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:13:00.123 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:13:00.146 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:14:00.074 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:14:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:14:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:14:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:15:00.080 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:15:00.101 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:15:00.123 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:15:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:16:00.083 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:16:00.104 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:16:00.127 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:16:00.149 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:17:00.077 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:17:00.099 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:17:00.121 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:17:00.143 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:18:00.074 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:18:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:18:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:18:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:19:00.074 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:19:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:19:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:19:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:20:00.074 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:20:00.095 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:20:00.117 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:20:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:21:00.075 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:21:00.095 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:21:00.115 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:21:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:22:00.068 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:22:00.089 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:22:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:22:00.129 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:23:00.071 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:23:00.092 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:23:00.112 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:23:00.133 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:24:00.063 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:24:00.084 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:24:00.105 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:24:00.126 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:25:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:25:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:25:00.139 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:25:00.159 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:26:00.071 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:26:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:26:00.131 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:26:00.151 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:27:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:27:00.160 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:27:00.220 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:27:00.333 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:28:00.092 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:28:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:28:00.146 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:28:00.172 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:29:00.075 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:29:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:29:00.117 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:29:00.178 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:30:00.070 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:30:00.090 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:30:00.112 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:30:00.175 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:31:00.066 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:31:00.087 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:31:00.108 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:31:00.129 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:32:00.065 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:32:00.085 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:32:00.105 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:32:00.125 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:33:00.073 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:33:00.094 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:33:00.115 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:33:00.136 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:34:00.063 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:34:00.084 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:34:00.104 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:34:00.124 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:35:00.061 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:35:00.082 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:35:00.102 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:35:00.124 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:36:00.073 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:36:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:36:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:36:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:37:00.077 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:37:00.103 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:37:00.127 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:37:00.151 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:38:00.076 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:38:00.100 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:38:00.123 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:38:00.147 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:39:00.083 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:39:00.107 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:39:00.131 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:39:00.154 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:40:00.078 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:40:00.102 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:40:00.125 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:40:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:41:00.073 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:41:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:41:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:41:00.143 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:42:00.079 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:42:00.104 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:42:00.127 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:42:00.151 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:43:00.081 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:43:00.104 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:43:00.128 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:43:00.152 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:44:00.082 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:44:00.106 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:44:00.132 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:44:00.157 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:45:00.095 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:45:00.131 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:45:00.158 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:45:00.184 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:46:00.081 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:46:00.107 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:46:00.133 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:46:00.159 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:47:00.081 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:47:00.107 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:47:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:47:00.162 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:48:00.083 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:48:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:48:00.136 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:48:00.163 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:49:00.090 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:49:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:49:00.144 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:49:00.171 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:50:00.081 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:50:00.108 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:50:00.134 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:50:00.160 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:51:00.093 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:51:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:51:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:51:00.171 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:52:00.088 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:52:00.113 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:52:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:52:00.167 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:53:00.076 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:53:00.099 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:53:00.121 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:53:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:54:00.075 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:54:00.098 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:54:00.121 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:54:00.144 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:55:00.078 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:55:00.101 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:55:00.124 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:55:00.147 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:56:00.069 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:56:00.092 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:56:00.115 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:56:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:57:00.080 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:57:00.103 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:57:00.126 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:57:00.149 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:58:00.081 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:58:00.104 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:58:00.127 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:58:00.149 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:59:00.081 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:59:00.103 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:59:00.126 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 10:59:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:00:00.081 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:00:00.103 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:00:00.126 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:00:00.149 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:01:00.083 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:01:00.105 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:01:00.129 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:01:00.150 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:02:00.076 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:02:00.100 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:02:00.122 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:02:00.144 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:03:00.079 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:03:00.100 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:03:00.121 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:03:00.142 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:04:00.066 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:04:00.087 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:04:00.108 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:04:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:05:00.078 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:05:00.099 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:05:00.120 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:05:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:06:00.078 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:06:00.101 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:06:00.121 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:06:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:07:00.069 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:07:00.090 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:07:00.111 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:07:00.132 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:08:00.075 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:08:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:08:00.117 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:08:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:09:00.077 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:09:00.105 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:09:00.128 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:09:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:10:00.086 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:10:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:10:00.134 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:10:00.157 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:11:00.078 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:11:00.101 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:11:00.124 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:11:00.147 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:12:00.079 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:12:00.102 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:12:00.126 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:12:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:13:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:13:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:13:00.143 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:13:00.166 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:14:00.069 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:14:00.093 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:14:00.115 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:14:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:15:00.069 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:15:00.091 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:15:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:15:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:16:00.080 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:16:00.104 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:16:00.128 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:16:00.151 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:17:00.081 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:17:00.104 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:17:00.127 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:17:00.150 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:18:00.087 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:18:00.113 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:18:00.139 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:18:00.164 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:19:00.078 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:19:00.103 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:19:00.126 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:19:00.180 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:20:00.082 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:20:00.107 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:20:00.131 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:20:00.156 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:21:00.080 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:21:00.105 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:21:00.129 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:21:00.155 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:22:00.084 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:22:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:22:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:22:00.160 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:23:00.090 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:23:00.115 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:23:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:23:00.165 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:24:00.087 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:24:00.111 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:24:00.137 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:24:00.162 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:25:00.085 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:25:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:25:00.134 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:25:00.159 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:26:00.088 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:26:00.112 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:26:00.137 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:26:00.160 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:27:00.084 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:27:00.112 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:27:00.153 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:27:00.175 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:28:00.080 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:28:00.103 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:28:00.126 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:28:00.149 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:29:00.080 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:29:00.104 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:29:00.126 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:29:00.149 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:30:00.072 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:30:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:30:00.120 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:30:00.143 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:31:00.090 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:31:00.114 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:31:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:31:00.164 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:32:00.079 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:32:00.102 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:32:00.125 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:32:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:33:00.076 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:33:00.100 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:33:00.123 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:33:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:34:00.083 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:34:00.108 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:34:00.132 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:34:00.156 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:35:00.072 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:35:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:35:00.120 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:35:00.142 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:36:00.072 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:36:00.094 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:36:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:36:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:37:00.075 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:37:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:37:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:37:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:38:00.066 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:38:00.088 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:38:00.111 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:38:00.133 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:39:00.070 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:39:00.092 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:39:00.114 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:39:00.137 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:40:00.064 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:40:00.086 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:40:00.108 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:40:00.131 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:41:00.077 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:41:00.099 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:41:00.121 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:41:00.143 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:42:00.083 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:42:00.105 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:42:00.128 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:42:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:43:00.083 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:43:00.106 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:43:00.129 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:43:00.152 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:44:00.083 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:44:00.105 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:44:00.128 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:44:00.150 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:45:00.081 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:45:00.103 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:45:00.125 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:45:00.147 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:46:00.069 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:46:00.091 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:46:00.114 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:46:00.136 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:47:00.068 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:47:00.090 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:47:00.113 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:47:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:48:00.069 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:48:00.091 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:48:00.113 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:48:00.136 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:49:00.069 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:49:00.091 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:49:00.113 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:49:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:50:00.081 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:50:00.103 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:50:00.126 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:50:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:51:00.069 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:51:00.093 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:51:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:51:00.137 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:52:00.071 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:52:00.092 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:52:00.113 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:52:00.134 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:53:00.081 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:53:00.102 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:53:00.123 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:53:00.166 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:54:00.076 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:54:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:54:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:54:00.139 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:55:00.073 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:55:00.094 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:55:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:55:00.137 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:56:00.074 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:56:00.095 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:56:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:56:00.137 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:57:00.070 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:57:00.092 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:57:00.114 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:57:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:58:00.063 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:58:00.084 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:58:00.104 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:58:00.125 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:59:00.079 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:59:00.101 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:59:00.124 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 11:59:00.147 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 12:00:00.075 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 12:00:00.096 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 12:00:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 12:00:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 12:01:00.065 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 12:01:00.087 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 12:01:00.109 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 12:01:00.131 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 12:02:00.076 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 12:02:00.098 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 12:02:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 12:02:00.142 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 12:03:00.073 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 12:03:00.094 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 12:03:00.115 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 12:03:00.137 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-17 12:03:07.087 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-17 12:03:07.282 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-17 12:03:07.282 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-17 12:03:07.282 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-17 12:03:07.283 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-17 12:03:07.454 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-17 12:03:07.702 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
